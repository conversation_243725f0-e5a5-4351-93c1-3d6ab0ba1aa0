import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')
  const isStaff = computed(() => user.value?.role === 'STAFF')

  // 模拟登录（用于测试）
  const login = async (loginData) => {
    try {
      isLoading.value = true
      console.log('尝试登录:', loginData)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 简单的测试账户验证
      if (loginData.username === 'admin' && loginData.password === '123456') {
        const mockUser = {
          id: 1,
          username: 'admin',
          name: '管理员',
          role: 'ADMIN',
          email: '<EMAIL>'
        }
        const mockToken = 'mock-jwt-token-' + Date.now()
        
        token.value = mockToken
        user.value = mockUser
        
        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('user', JSON.stringify(user.value))
        
        ElMessage.success('登录成功')
        
        // 跳转到仪表盘
        router.push('/dashboard')
        
        return { data: { token: mockToken, user: mockUser } }
      } else if (loginData.username === 'staff' && loginData.password === '123456') {
        const mockUser = {
          id: 2,
          username: 'staff',
          name: '服务员',
          role: 'STAFF',
          email: '<EMAIL>'
        }
        const mockToken = 'mock-jwt-token-' + Date.now()
        
        token.value = mockToken
        user.value = mockUser
        
        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('user', JSON.stringify(user.value))
        
        ElMessage.success('登录成功')
        
        // 跳转到仪表盘
        router.push('/dashboard')
        
        return { data: { token: mockToken, user: mockUser } }
      } else {
        throw new Error('用户名或密码错误')
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      isLoading.value = true
      console.log('尝试注册:', registerData)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('注册成功，请登录')
      return { data: { message: '注册成功' } }
    } catch (error) {
      console.error('注册失败:', error)
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (showMessage = true) => {
    try {
      console.log('用户登出')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = ''
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 只有在主动登出时才跳转和显示消息
      if (showMessage) {
        router.push('/login')
        ElMessage.success('已退出登录')
      }
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    try {
      const savedToken = localStorage.getItem('token')
      const savedUser = localStorage.getItem('user')
      
      if (savedToken && savedUser) {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        console.log('从本地存储恢复用户状态:', user.value)
      }
    } catch (error) {
      console.error('检查认证状态失败:', error)
      // 清除无效数据
      logout(false)
    }
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    try {
      isLoading.value = true
      console.log('修改密码:', passwordData)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('密码修改成功')
      return { data: { message: '密码修改成功' } }
    } catch (error) {
      console.error('密码修改失败:', error)
      ElMessage.error(error.message || '密码修改失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isStaff,
    
    // 方法
    login,
    register,
    logout,
    checkAuth,
    updateUser,
    changePassword,
  }
})
